package com.mall.project.dao.transactionDataTotal;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@Repository
public class TransactionDataTotalDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    // 获取累计交易金额
    public String sumTradeAmountTotal(String phone,String startDate) {
        List<Object> params = new ArrayList<>();
        // 构建查询语句
        String sql = "select sum(trade_amount) as trade_amount_total from enterprise_product_data WHERE status = '0'";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(update_time) <= ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_time) <= CURDATE() - INTERVAL 1 DAY";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, String.class);
        }else{
            return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
        }
    }

    // 查询统计累计交易数据导出
    public List<Map<String, Object>> transactionDataTotalExport(String phone,String startDate, String endDate) {
        return QuerytransactionDataTotalPages(phone,startDate,endDate,1000000,0);
    }
    // 查询统计累计交易数据 分页显示
    public List<Map<String, Object>> QuerytransactionDataTotalPages(String phone, String startDate, String endTDate, int limit, int offset) {
        // 构建查询语句
        String sql = "SELECT DATE(p.update_time) as update_time,p.phone,p.company_name,p.trade_name,sum(total_count) as trade_amount from enterprise_product_data p WHERE p.trade_name IS NOT NULL AND p.trade_amount IS NOT NULL";
        if (phone != null && !phone.isEmpty()) {
            sql += "    AND p.phone LIKE '%" + phone + "%'\n";
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += "    AND p.update_time = '" + startDate + "'\n";
        }else{
            sql += "    AND p.update_time <= CURDATE() \n";
        }
        sql += "group by DATE(p.update_time),p.phone,p.company_name,p.trade_name \n" +
                " ORDER BY DATE(p.update_time) DESC LIMIT " + limit + " OFFSET " + offset;
        return jdbcTemplate.queryForList(sql);
    }
    // 查询统计累计交易数据 总条数
    public int countTransactionDataTotal(String phone, String startDate, String endTDate) {
        // 构建查询语句
        String sql = "SELECT COUNT(*) FROM enterprise_product_data WHERE trade_name IS NOT NULL AND trade_amount IS NOT NULL";
        if (phone != null && !phone.isEmpty()) {
            sql += "    AND phone LIKE '%" + phone + "%'\n";
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += "    AND update_time = '" + startDate + "'\n";
        }else{
            sql += "    AND update_time <= CURDATE() \n";
        }
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }
}
