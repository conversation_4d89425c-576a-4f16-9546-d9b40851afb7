package com.mall.project.controller.systemInfo;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.project.dto.systemInfo.SystemInfo;
import com.mall.project.service.systemInfo.SystemInfoService;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 系统信息控制器
 */
@RestController
@RequestMapping("/api")
public class SystemInfoController {

    @Autowired
    private SystemInfoService systemInfoService;

    /**
     * 查询系统信息, 分页显示
     */
    @PostMapping("/querySystemInfo")
    public CommonResult<CommonPage<Map<String, Object>>> querySystemInfo(@RequestBody @Valid SystemInfo pojo) {
        CommonPage<Map<String, Object>> commonPage = systemInfoService.querySystemInfoPages(pojo.getStartDate(), pojo.getEndDate(), pojo.getPageNum(), pojo.getPageSize());
        return CommonResult.success(commonPage);
    }


    /**
     * 统计每日数据累计量
     */
    @PostMapping("/dailyDataAccrueTotal")
    public CommonResult<String> dailyDataAccrueTotal(@RequestBody @Valid SystemInfo pojo) {
        String dailyDataAccrueTotal = systemInfoService.dailyDataAccrueTotal(pojo.getStartDate());
        return CommonResult.success(dailyDataAccrueTotal);
    }

    /**
     * 统计 系统每日余数
     */
    @PostMapping("/dailyRemainderTotal")
    public CommonResult<String> dailyRemainderTotal(@RequestBody @Valid SystemInfo pojo) {
        String dailyRemainderTotal = systemInfoService.dailyRemainderTotal(pojo.getStartDate());
        return CommonResult.success(dailyRemainderTotal);
    }

    /**
     * 统计 每日系统量化数总累计
     */
    @PostMapping("/dailyQuantityTotal")
    public CommonResult<String> dailyQuantityTotal(@RequestBody @Valid SystemInfo pojo) {
        String dailyQuantityTotal = systemInfoService.dailyQuantityTotal(pojo.getStartDate());
        return CommonResult.success(dailyQuantityTotal);
    }

    /**
     * 统计每日计量数
     */
    @PostMapping("/dailyMeterageTotal")
    public CommonResult<String> dailyMeterageTotal(@RequestBody @Valid SystemInfo pojo) {
        String dailyMeterageTotal = systemInfoService.dailyMeterageTotal(pojo.getStartDate());
        return CommonResult.success(dailyMeterageTotal);
    }
}
