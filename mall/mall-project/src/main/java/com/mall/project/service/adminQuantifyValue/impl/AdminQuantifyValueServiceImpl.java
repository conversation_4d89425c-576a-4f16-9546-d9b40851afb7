package com.mall.project.service.adminQuantifyValue.impl;

import com.mall.common.api.CommonPage;
import com.mall.common.util.ConvertToCamelCase;
import com.mall.project.dao.adminQuantifyValue.AdminQuantifyValueDao;
import com.mall.project.exception.BusinessException;
import com.mall.project.service.adminQuantifyValue.AdminQuantifyValueService;
import com.mall.project.service.quantizationValue.QuantizationValueService;
import lombok.Getter;
import lombok.Setter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Admin 量化值接口实现类
 */
@Service
@Slf4j
public class AdminQuantifyValueServiceImpl implements AdminQuantifyValueService {

    @Autowired
    private AdminQuantifyValueDao adminQuantifyValueDao;

    @Autowired
    private QuantizationValueService quantizationValueService;

    /**
     * 每日Admin量化值计算
     */
    @Override
    public void updateAdminQuantifyValue(String startTime) {
        // 每日累计Admin量化值
        String adminDailyQuantizationValue = quantizationValueService.adminDailyQuantizationValue(startTime);
        //查询最新日期的量化率
        String latestQuantizationRate = quantizationValueService.getLatestQuantizationRate(startTime);

        BigDecimal AdminQuantifyValue = new BigDecimal(adminDailyQuantizationValue).multiply(new BigDecimal(latestQuantizationRate));
        adminQuantifyValueDao.saveAdminQuantifyValue(AdminQuantifyValue.toString());
    }

    /**
     * 查询Admin量化值, 分页显示
     */
    @Override
    public CommonPage<Map<String, Object>> queryAdminAdminQuantifyValuePages(String phone, String startDate, String endDate, int pageNum, int pageSize) {
        // 验证开始日期格式 yyyy-MM-dd
        if (startDate != null && !startDate.isEmpty() && !startDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("开始时间格式不正确，请输入yyyy-MM-dd格式");
        }
        // 验证结束日期格式 yyyy-MM-dd
        if (endDate != null && !endDate.isEmpty() && !endDate.matches("^\\d{4}-\\d{2}-\\d{2}$")) {
            throw new BusinessException("结束时间格式不正确，请输入yyyy-MM-dd格式");
        }
        if (pageNum < 1) {
            pageNum = 1;
        }
        if (pageSize < 1) {
            pageSize = 10;
        }

        int offset = (pageNum - 1) * pageSize;
        List<Map<String, Object>> dataList = adminQuantifyValueDao.queryAdminAdminQuantifyValuePages(phone, startDate, endDate, pageSize, offset);
        // 转换下划线格式为驼峰格式
        List<Map<String, Object>> formattedDataList = dataList.stream().map(ConvertToCamelCase::convertToCamelCase).toList();
        long total = adminQuantifyValueDao.totalAdminAdminQuantifyValueCount(phone, startDate, endDate);
        int totalPages = (total == 0) ? 0 : (int) Math.ceil((double) total / pageSize);
        AdminQuantifyValueServiceImpl.CustomCommonPage<Map<String, Object>> commonPage = new AdminQuantifyValueServiceImpl.CustomCommonPage<>(pageNum, pageSize, totalPages, total, formattedDataList);
        // 添加汇总数据到返回结果中
        Map<String, Object> summary = new HashMap<>();
        summary.put("todayAdminAdminQuantifyValue", ConvertToCamelCase.formatDecimal(new BigDecimal(todayAdminAdminQuantifyValue(phone,startDate))));   //今日Admin量化值
        summary.put("totalAdminAdminQuantifyValue", ConvertToCamelCase.formatDecimal(new BigDecimal(totalAdminAdminQuantifyValue(phone,startDate))));   //累计Admin量化值
        commonPage.setSummary(summary);
        return commonPage;
    }

    // 新增内部类扩展CommonPage
    @Setter
    @Getter
    private static class CustomCommonPage<T> extends CommonPage<T> {
        private Map<String, Object> summary;
        public CustomCommonPage(int pageNum, int pageSize, int totalPage, long total, List<T> list) {
            super(pageNum, pageSize, totalPage, total, list);
        }
    }

    /**
     * 今日Admin量化值
     */
    @Override
    public String todayAdminAdminQuantifyValue(String phone,String startTime) {
        return adminQuantifyValueDao.todayAdminAdminQuantifyValue(phone,startTime) == null ? "0" : adminQuantifyValueDao.todayAdminAdminQuantifyValue(phone,startTime);
    }

    /**
     * 累计Admin量化值
     */
    @Override
    public String totalAdminAdminQuantifyValue(String phone,String startDate) {
        return adminQuantifyValueDao.totalAdminAdminQuantifyValue(phone,startDate) == null ? "0" : adminQuantifyValueDao.totalAdminAdminQuantifyValue(phone,startDate);
    }

    /**
     * 导出Admin量化值 Excel
     */
    @Override
    public List<Map<String, Object>> exportAdminAdminQuantifyValueExcel(String phone, String startDate, String endDate) {
        List<Map<String, Object>> dataList = adminQuantifyValueDao.exportAdminAdminQuantifyValueExcel(phone,startDate, endDate);
        if(dataList.isEmpty()){
            throw new RuntimeException("暂无数据");
        }
        return dataList.stream().map(ConvertToCamelCase::convertToCamelCase).collect(java.util.stream.Collectors.toList());
    }

}
