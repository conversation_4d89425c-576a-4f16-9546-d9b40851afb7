package com.mall.project.dao.statusSet;


import com.mall.project.dto.statusSet.StatusSet;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Map;

/**
 * 状态设置数据访问对象
 */
@Repository
@Slf4j
public class StatusSetDao {

    @Autowired
    private JdbcTemplate jdbcTemplate;


    /**
     * 保存或更新状态设置
     */
    public int saveOrUpdateStatusSet(StatusSet pojo, Integer updatePerson) {
        try {
            // 先查询是否存在该 type 的记录
            Integer count = jdbcTemplate.queryForObject("SELECT COUNT(*) FROM status_set WHERE type = ?", Integer.class, pojo.getType());
            if (count > 0) {
                // 存在则更新
                String updateSql = "UPDATE status_set SET is_enabled = ?, update_person = ?, update_time = NOW() WHERE type = ?";
                return jdbcTemplate.update(updateSql, pojo.getIsEnabled(), updatePerson, pojo.getType());
            } else {
                // 不存在则插入
                String insertSql = "INSERT INTO status_set(is_enabled, type, update_person, update_time) VALUES (?, ?, ?, NOW())";
                return jdbcTemplate.update(insertSql, pojo.getIsEnabled(), pojo.getType(), updatePerson);
            }
        } catch (Exception e) {
            log.error("保存或更新状态设置失败: {}", e.getMessage());
            throw new RuntimeException("保存或更新状态设置失败: " + e.getMessage());
        }
    }

    /**
     * 查询状态设置
     */
    public Map<String, Object> getStatusSet(String type) {
        try{
            return jdbcTemplate.queryForMap("SELECT is_enabled, type FROM status_set WHERE type = ?", type);
        }catch (EmptyResultDataAccessException e){
            log.info("未找到状态设置信息，请设置状态设置");
            return null;
        }
    }

    /**
     * 量化值 统计
     */
    public String quantizationValue(){
        String sql = "select sum(c.quantify_count) as quantify_count from mall_b_users u,mall_b_users_count c\n" +
                "where u.phone = c.phone\n" +
                "and u.`status` <> 0\n" +
                "and c.quantify_count >0 " +
                "and u.user_type = 'B' " +
                "and DATE(c.update_time) = CURDATE() - INTERVAL 1 DAY";
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 量化值 输出
     */
    @Transactional
    public int outputQuantizationValue(String phone,String quantifyValue, int updatePerson){
        // 查看function_datas phone 是否已经存在,如果存在则更新,不存在则插入
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM function_datas WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0) {
            String sql = "INSERT INTO function_datas(enterprise_id,phone,quantify_value,update_person,update_date)VALUES (?,?,?,?,CURDATE() )";
            jdbcTemplate.update(sql, 1, phone, quantifyValue, updatePerson);
        }else{
            String sql = "UPDATE function_datas SET quantify_value = COALESCE(quantify_value, 0) + ?,update_person = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(sql, quantifyValue, updatePerson, phone);
        }
        String sql = "UPDATE quantify_subsidy_total SET quantify = COALESCE(quantify, 0) - ? WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
        jdbcTemplate.update(sql, quantifyValue);
        // 把量化值记录到 quantization_value 表中, 对应的 phone
        if(jdbcTemplate.queryForObject("SELECT EXISTS(select 1 from quantization_value where phone = ? AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY)", Integer.class, phone) == 0){
             sql = "INSERT INTO quantization_value(phone,value,total_value,update_date)VALUES(?,?,?,CURDATE() - INTERVAL 1 DAY)";
            jdbcTemplate.update(sql, phone, quantifyValue, new BigDecimal(quantifyValue).add(new BigDecimal(sumValue(phone))));
        }else{
             sql = "UPDATE quantization_value SET value = value + ?,total_value = total_value + ? WHERE phone = ? AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            jdbcTemplate.update(sql, quantifyValue, quantifyValue, phone);
        }
        return 1;
    }

    /**
     * 统计 value ,phone 的和
     */
    public String sumValue(String phone) {
        try{
            String sql = "SELECT SUM(value) FROM quantization_value WHERE phone = ? AND DATE(update_date) < CURDATE() - INTERVAL 1 DAY";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 统计 platform_gold ,phone 的和
     */
    public String sumPlatformGold(String phone) {
        try{
            String sql = "SELECT SUM(platform_gold) FROM quantization_value WHERE phone = ? AND DATE(update_date) < CURDATE() - INTERVAL 1 DAY";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 流失的 补贴金 统计
     */
    public String lostSubsidy(){
        // 使用COALESCE或IFNULL函数处理NULL值，直接在SQL中返回0
        String sql = "SELECT COALESCE(SUM(value), 0) as subsidy_funds FROM csubsidy_result WHERE DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 补贴金 输出
     */
    @Transactional
    public int outputSubsidy(String phone,String subsidy, int updatePerson){
        // 查看function_datas phone 是否已经存在,如果存在则更新,不存在则插入
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM function_datas WHERE phone = ? AND DATE(update_date) = CURDATE())", Integer.class, phone) == 0) {
            String sql = "INSERT INTO function_datas(enterprise_id,phone,subsidy_funds,update_person,update_date)VALUES (?,?,?,?,CURDATE())";
            jdbcTemplate.update(sql, 1, phone, subsidy, updatePerson);
        }else{
            String sql = "UPDATE function_datas SET subsidy_funds = COALESCE(subsidy_funds, 0) + ?, update_person = ? WHERE phone = ? AND update_date = CURDATE() ";
            jdbcTemplate.update(sql, subsidy, updatePerson, phone);
        }
        String sql = "UPDATE quantify_subsidy_total SET subsidy = COALESCE(subsidy, 0) - ? WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
        jdbcTemplate.update(sql, subsidy);
        // 把补贴金记录到 quantization_value 表中, 对应的 phone
        if(jdbcTemplate.queryForObject("SELECT EXISTS(select 1 from quantization_value where phone = ? AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY)", Integer.class, phone) == 0){
            sql = "INSERT INTO quantization_value(phone,platform_gold,total_platform_gold,update_date)VALUES(?,?,?,CURDATE() - INTERVAL 1 DAY)";
            jdbcTemplate.update(sql, phone, subsidy, new BigDecimal(subsidy).add(new BigDecimal(sumPlatformGold(phone))));
        }else{
            sql = "UPDATE quantization_value SET platform_gold = platform_gold + ?,total_platform_gold = total_platform_gold + ? WHERE phone = ? AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            jdbcTemplate.update(sql, subsidy, subsidy, phone);
        }
        return 1;
    }

    /**
     * 更新量化值和补贴金
     */
    public void updateQuantifyAndSubsidy(String quantifyValue, String subsidy) {
        // 插入数据到quantify_subsidy_total 表,如果update_date 已经存在则更新,不存在则插入,使用SELECT EXISTS 判断
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM quantify_subsidy_total WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY)", Integer.class) == 0) {
            String sql = "INSERT INTO quantify_subsidy_total(quantify,subsidy,update_date)VALUES(?, ?, CURDATE() - INTERVAL 1 DAY)";
            jdbcTemplate.update(sql, quantifyValue, subsidy);
        }else{
            String sql = "UPDATE quantify_subsidy_total SET quantify = ?,subsidy = ? WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            jdbcTemplate.update(sql, quantifyValue, subsidy);
        }
    }

    /**
     * 查询量化值和补贴金
     */
    public Map<String, Object> getQuantifyAndSubsidy() {
        try {
            String sql = "SELECT quantify,subsidy FROM quantify_subsidy_total WHERE DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            return jdbcTemplate.queryForMap(sql);
        } catch (EmptyResultDataAccessException e) {
            //log.info("未找到昨天的量化值和补贴金数据");
            return null;
        }
    }
}
