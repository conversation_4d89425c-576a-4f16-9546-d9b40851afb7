package com.mall.project.dao.areaAuthorize;

import com.mall.project.dto.areaAuthorize.AreaAuthorize;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 区域授权数据层
 */
@Repository
@Slf4j
public class AreaAuthorizeDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取区域
     */
    public List<Map<String,Object>> getArea(String areaId){
        if (!areaId.isEmpty()) {
            return jdbcTemplate.queryForList("select id,name from area where parent_id = ?", areaId);
        }else{
            return jdbcTemplate.queryForList("select id,name from area where level = 1");
        }
    }

    /**
     * 获取合作企业
     */
    public List<Map<String,Object>> getCooperateEnterprise(){
        String sql = "SELECT e.id,e.`name` as enterprise_name,s.on_off FROM cooperate_enterprise e LEFT JOIN area_authorize_set s ON e.id = s.enterprise_id WHERE e.`status` = 0";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 对手机号授权
     */
    @Transactional
    public int saveAreaAuthorize(AreaAuthorize pojo, int updatePerson) {
        try {
            // 先插入数据到 area_authorize_set 表,再插入数据到area_authorize 表
            //如果 area_authorize_set 表的 enterprise_id 不存在则插入数据,存在则更新数据
            if (isNotEmpty(pojo.getEnterpriseId()) && jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM area_authorize_set WHERE enterprise_id = ?)", Integer.class, pojo.getEnterpriseId()) == 0) {
                String insertSql = "INSERT INTO area_authorize_set(enterprise_id,on_off,update_person,update_time)VALUES (?,?,?,NOW()) ";
                jdbcTemplate.update(insertSql, pojo.getEnterpriseId(), pojo.getOnOff(), updatePerson);
            }else{
                String updateSql = "UPDATE area_authorize_set SET on_off = ?,update_person = ?,update_time = NOW()";
                if(isNotEmpty(pojo.getEnterpriseId())){
                    updateSql +=  " WHERE enterprise_id = ?";
                    jdbcTemplate.update(updateSql, pojo.getOnOff(), updatePerson,pojo.getEnterpriseId());
                }else{
                    jdbcTemplate.update(updateSql, pojo.getOnOff(), updatePerson);
                }
            }
            if(isNotEmpty(pojo.getEnterpriseId()) && isNotEmpty(pojo.getPhone()) && isNotEmpty(pojo.getStartTime()) && isNotEmpty(pojo.getEndTime()) && isNotEmpty(pojo.getAreaId()) && isNotEmpty(pojo.getType()) && pojo.getOnOff().equals("0")){  //当开启的时候 插入数据到 area_authorize 表
                // 判断 pojo.getPhone() 如果该手机号不在  mall_b_users 表中则不允许授权
                if(!pojo.getPhone().isEmpty()){
                    if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM mall_b_users WHERE phone = ?)", Integer.class, pojo.getPhone()) == 0) {
                        return -3;
                    }
                }
                // 判断 pojo.getPhone() 手机号码是否已经授权，如果已经授权则不允许再授权
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM area_authorize WHERE phone = ?)", Integer.class, pojo.getPhone()) > 0) {
                    return -1;
                }
                // 判断 pojo.getAreaId() 如果该区域已经授权则不允许再授权
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM area_authorize WHERE area_id = ?)", Integer.class, pojo.getAreaId()) > 0) {
                    return -2;
                }
                //判断 pojo.getPhone() 和 type 是否 和 mall_b_users 表 中的 user_type 是否一致，如果不一致则不允许授权
                String userType = jdbcTemplate.queryForObject("SELECT user_type FROM mall_b_users WHERE phone = ?", String.class, pojo.getPhone());
                // 用户类型为B的可以进行B授权, 用户类型为C的不能给C授权,只有用户类型为CB的才能授权C
                if ("C".equals(userType)) {
                    // C用户禁止所有授权
                    return -4;
                } else if ("B".equals(userType) && !"B".equals(pojo.getType())) {
                    // B用户要求授权类型必须是B
                    return -4;
                } else if ("CB".equals(userType) && !"C".equals(pojo.getType())) {
                    // CB用户要求授权类型必须是C
                    return -4;
                }
                String sql = "INSERT INTO area_authorize(phone,area_id,start_time,end_time,type,update_person,update_time)VALUES (?,?,?,?,?,?,CURDATE() - INTERVAL 1 DAY + INTERVAL 5 MINUTE)";
                return jdbcTemplate.update(connection -> {
                    var ps = connection.prepareStatement(sql);
                    try {
                        ps.setString(1, pojo.getPhone());
                        ps.setString(2, pojo.getAreaId());
                        ps.setString(3, pojo.getStartTime());
                        ps.setString(4, pojo.getEndTime());
                        ps.setString(5, pojo.getType());
                        ps.setInt(6, updatePerson);
                        return ps;
                    } catch (Exception e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            return 1;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 查询授权信息
     */
    public List<Map<String,Object>> queryAreaAuthorize(String type,Integer level,int limit, int offset){
        try{
            String sql = "SELECT u.id,u.phone,b.username,a.`name` as area_name,u.start_time,u.end_time,u.value FROM area_authorize u,area a,mall_b_users b " +
                    " WHERE u.area_id = a.id" +
                    " AND u.phone = b.phone" +
                    " And b.user_type = ?" +
                    " AND a.`level` = ?" +
                    " ORDER BY update_time DESC LIMIT " + limit + " OFFSET " + offset;
            List<Map<String, Object>> dataList = jdbcTemplate.queryForList(sql, type, level);
            //如果数据库无数据则返回空列表
            if(dataList.isEmpty()){
                return null;
            }
            return dataList;
        }catch (Exception e){
            throw new RuntimeException(e);
        }
    }
    /**
     * 查询授权信息 总条数
     */
    public int areaAuthorizeCount(String type,Integer level){
        String sql = "SELECT count(1) as count FROM area_authorize u,area a,mall_b_users b " +
                " WHERE u.area_id = a.id" +
                " AND u.phone = b.phone" +
                " And b.user_type = ?" +
                " AND a.`level` = ?" +
                " ORDER BY update_time DESC";
        return jdbcTemplate.queryForObject(sql, Integer.class, type, level);
    }
    /**
     * 判断区域授权是否开启
     */
    public String checkAreaAuthorizeSet(){
        String sql = "SELECT on_off FROM area_authorize_set WHERE enterprise_id = 1";   // 判断区域授权是否开启
        return jdbcTemplate.queryForObject(sql, String.class);
    }

    /**
     * 从area 表的level = 4 往上找代理CB
     */
    public String queryAreaAuthorizePhone(String areaId){
        try {
            String sql = "select phone from area_authorize where type = 'C' and area_id = ? and NOW() >= start_time and NOW() <= end_time";
            return jdbcTemplate.queryForObject(sql, String.class, areaId);
        } catch (Exception e) {
            // 如果没有找到记录，返回null而不是抛出异常
            return null;
        }
    }
    /**
     * 查找areaId 的父id
     */
    public String queryParentId(String areaId){
        try {
            String sql = "select parent_id from area where id = ?";
            return jdbcTemplate.queryForObject(sql, String.class, areaId);
        } catch (Exception e) {
            // 如果没有找到记录，返回null而不是抛出异常
            return null;
        }
    }

    /**
     * 查询C用户分量达标状态
     */
    public List<Map<String,Object>> queryCWeightStatus(){
        String sql = "select u.flag,c.Weight,u.deduction_money_limit,u.phone,u.town_code from mall_b_users u,mall_b_users_count c " +
                "where u.phone = c.phone " +
                "and user_type = 'C' " +
                "and status = 0 " +
                "and c.Weight > 0 " +
                "and DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 查询C用户状态为异常的用户
     */
    public List<Map<String,Object>> queryCStatusException(){
        String sql = "select u.`status`,u.flag,c.Weight,u.deduction_money_limit,u.phone,u.town_code from mall_b_users u,mall_b_users_count c \n" +
                "                where u.phone = c.phone \n" +
                "                and user_type = 'C' \n" +
                "                and status <> 0 \n" +
                "                and c.Weight > 0 \n" +
                "                and DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 更新C的补贴金
     */
    public void updateCSubsidy(String phone, BigDecimal subsidy) {
        String sql = "UPDATE area_authorize SET value = value + ? WHERE phone = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
        jdbcTemplate.update(sql, subsidy, phone);
    }

    /**
     * C用户补贴金流失记录
     */
    public void insertCSubsidyLossRecord(String lostPhone, String givePhone, BigDecimal value,String reason) {
        // 如果当天的 update_time 已经存在记录则更新 value = value + ?
        if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM CSubsidy_result WHERE lost_phone = ? AND give_phone = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY)", Integer.class, lostPhone , givePhone) > 0) {
            String sql = "UPDATE CSubsidy_result SET value = ? WHERE lost_phone = ? AND give_phone = ? AND DATE(update_time) = CURDATE() - INTERVAL 1 DAY";
            jdbcTemplate.update(sql, value, lostPhone, givePhone);
        }else{
            String sql = "INSERT INTO CSubsidy_result(lost_phone,give_phone,value,reason,update_time)VALUES (?, ?,?,?, CURDATE() - INTERVAL 1 DAY)";
            jdbcTemplate.update(sql, lostPhone, givePhone, value, reason);
        }
    }

    /**
     * 获取用户所在区域的完整层级路径（从level=1到用户所在的level）
     */
    public List<Map<String, Object>> getUserAreaHierarchy(String townCode) {
        try {
            String sql = "WITH RECURSIVE area_hierarchy AS (" +
                    "    SELECT id, parent_id, level " +
                    "    FROM area " +
                    "    WHERE id = ? " +
                    "    UNION ALL " +
                    "    SELECT a.id, a.parent_id, a.level " +
                    "    FROM area a " +
                    "    JOIN area_hierarchy ah ON a.id = ah.parent_id " +
                    "    WHERE ah.level > 1 " +
                    ") " +
                    "SELECT id, level FROM area_hierarchy ORDER BY level";
            return jdbcTemplate.queryForList(sql, townCode);
        } catch (Exception e) {
            return List.of();
        }
    }
     /**
     * 删除授权信息
     */
    @Transactional
    public int deleteAreaAuthorize(Integer id) {
        try {
            // 检查授权记录是否存在
            if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM area_authorize WHERE id = ?)", Integer.class, id) == 0) {
                return 0; // 记录不存在
            }
            // 删除授权记录
            String sql = "DELETE FROM area_authorize WHERE id = ?";
            return jdbcTemplate.update(sql, id);
        } catch (Exception e) {
            throw new RuntimeException("删除授权信息失败", e);
        }
    }
}
