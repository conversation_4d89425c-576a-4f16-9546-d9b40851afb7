package com.mall.project.dao.functionDatas;

import com.mall.project.dao.areaAuthorize.AreaAuthorizeDao;
import com.mall.project.dao.dailyTradePercentage.DailyTradePercentageDao;
import com.mall.project.dto.functionDatas.FunctionDatas;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.EmptyResultDataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * 功能数据数据访问对象
 */
@Repository
@Slf4j
public class FunctionDatasDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private DailyTradePercentageDao dailyTradePercentageDao;

    @Autowired
    private AreaAuthorizeDao areaAuthorizeDao;

    /**
     * 新增功能数据
     */
    public int addFunctionDatas(FunctionDatas pojo, int updatePerson) {
        // 判断如果 enterpriseId、phone、update_date 已经存在,则更新数据,不存在则插入数据
        String sql = "SELECT EXISTS(SELECT 1 FROM function_datas WHERE phone = ? AND DATE(update_date) = CURDATE())";
        if (jdbcTemplate.queryForObject(sql, Integer.class, pojo.getPhone()) > 0) {
            sql = "UPDATE function_datas SET value = ? WHERE phone = ? AND DATE(update_date) = CURDATE()";
            return jdbcTemplate.update(sql, pojo.getValue(), pojo.getPhone());
        }else{
            sql = "INSERT INTO function_datas(enterprise_id,phone,value,update_person,update_date)VALUES (1,?,?,?,CURDATE())";
            return jdbcTemplate.update(sql, pojo.getPhone(), pojo.getValue(), updatePerson);
        }
    }
    /**
     * 查询功能数据,分页显示
     */
    public List<Map<String, Object>> queryFunctionDatasPages(String phone, String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date,phone,value,quantify_value,subsidy_funds FROM function_datas WHERE 1=1";
        if (phone != null && !phone.trim().isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }
        sql += " ORDER BY update_date DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 查询功能数据总条数
     */
    public int queryFunctionDatasCount(String phone, String startTime, String endTime) {
        try {
            List<Object> params = new ArrayList<>();
            String sql = "SELECT COUNT(*) FROM function_datas WHERE 1=1";
            if (phone != null && !phone.trim().isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startTime);
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, Integer.class);
            }else{
                return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
            }
        } catch (Exception e) {
            return 0;
        }
    }
    /**
     * 今日总功能数值
     */
    public String todayTotalFunctionDatas(String phone,String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(value), 0) as today_total_function_datas FROM function_datas WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startDate != null && !startDate.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 今日总量化值
     */
    public String todayTotalQuantify(String phone,String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(quantify_value), 0) as today_total_quantify FROM function_datas WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startDate != null && !startDate.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 今日总补贴金
     */
    public String todayTotalSubsidy(String phone,String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(subsidy_funds), 0) as today_total_subsidy FROM function_datas WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startDate != null && !startDate.isEmpty()) {
                sql += " AND DATE(update_date) = ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 今日总累计功能数值
     */
    public String todayAllTotalFunctionDatas (String phone,String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(value), 0) as todayAllTotalFunctionDatas FROM function_datas WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startDate != null && !startDate.isEmpty()) {
                sql += " AND DATE(update_date) <= ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) <= CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 今日总累计量化值
     */
    public String todayAllTotalQuantify(String phone,String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(quantify_value), 0) as todayAllTotalQuantify FROM function_datas WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startDate != null && !startDate.isEmpty()) {
                sql += " AND DATE(update_date) <= ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) <= CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 今日总累计补贴金
     */
    public String todayAllTotalSubsidy(String phone,String startDate){
        List<Object> params = new ArrayList<>();
        try {
            String sql = "SELECT COALESCE(sum(subsidy_funds), 0) as todayAllTotalSubsidy FROM function_datas WHERE 1 = 1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startDate != null && !startDate.isEmpty()) {
                sql += " AND DATE(update_date) <= ?";
                params.add(startDate);
            }else{
                sql += " AND DATE(update_date) <= CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 导出功能数据 Excel
     */
    public List<Map<String, Object>> exportFunctionDatasExcel(String phone,String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date,phone,value,quantify_value,subsidy_funds FROM function_datas WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }
        sql += " ORDER BY update_date DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     *  查询要更新的数据,更新昨天的数据
     */
    public List<Map<String, Object>> updateQuantifyCount(){
        String sql = "SELECT phone,value FROM function_datas WHERE `value` is not null and DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
        return jdbcTemplate.queryForList(sql);
    }

    /**
     * 所有企业各ID每日每笔新交易数据的量化数比 排位 权限
     */
    public List<Map<String, Object>> getDailyTradePercentage(){
        String sql = "SELECT daily_trade_percentage,ranking1,ranking1_percentage,ranking2,ranking2_percentage FROM daily_trade_percentage WHERE is_enabled = 0";
        try {
            return jdbcTemplate.queryForList(sql);
        } catch (EmptyResultDataAccessException e) {
            return null;
        }
    }

    /**
     * 查询所有合作企业Admain的各ID每日每笔数据量化数比
     */
    public String getPartnerEnterpriseAdminData(){
        try {
            String sql = "SELECT daily_data_percentage FROM partner_enterprise_admin_data WHERE is_enabled = 0";
            return jdbcTemplate.queryForObject(sql, String.class);
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 将功能数据更新到量化数
     */
    public void updateQuantifyCount(String phone, BigDecimal value){
        String sql = "UPDATE mall_b_users_count SET quantify = quantify + ?,quantify_count = quantify_count + ? WHERE phone = ? and DATE(update_time) = CURDATE()";
        jdbcTemplate.update(sql, value,value, phone);
    }

    /**
     * 将功能数据更新到Admin的每日累计量化数
     */
    public void updateAdminDailyQuantity(BigDecimal value){
        String sql = "UPDATE enterprise_trade_stats SET admin_daily_quantity = admin_daily_quantity + ? WHERE enterprise_id = 1 AND DATE(update_time) = CURDATE()";
        jdbcTemplate.update(sql, value);
    }

    public void updatemallBUsersCountByRanking(Integer ranking, BigDecimal rankingPercentage){
        try{
            String sql = "SELECT d.phone,d.`value`,u.jurisdiction FROM function_datas d,mall_b_users u WHERE d.phone = u.phone AND DATE(d.update_date) = CURDATE() - INTERVAL 1 DAY";
            List<Map<String, Object>> mapsList = jdbcTemplate.queryForList(sql);
            for (Map<String, Object> map : mapsList) {
                String phone = map.get("phone").toString();
                int jurisdiction = Integer.parseInt(map.get("jurisdiction").toString());   // 原用户权限：权限1：1，权限2：2，权限3：3
                int willCompareJurisdiction = 0;
                String upRankingPhone = "";
                boolean skipCurrentIteration = false;
                do{
                    Map<String, Object> levelDataMap = dailyTradePercentageDao.judgeUserLevel(phone, ranking);
                    if (levelDataMap.isEmpty()) {
                        skipCurrentIteration = true;  // 设置标志，跳过当前循环
                        break;
                    }
                    // 判断权限 jurisdiction 如果小于要比较的权限，则跳过
                    willCompareJurisdiction = Integer.parseInt(levelDataMap.get("jurisdiction").toString());
                    upRankingPhone = levelDataMap.get("phone").toString();
                    ranking += 1;
                }while(willCompareJurisdiction < jurisdiction);

                if (skipCurrentIteration) {
                    continue; // 跳到下一次for循环
                }
                BigDecimal totalCount = new BigDecimal(map.get("value").toString());
                BigDecimal quantifyCount = totalCount.multiply(rankingPercentage.divide(new BigDecimal(100)));
                String updateSql = "UPDATE mall_b_users_count set quantify = quantify + ?,quantify_count = quantify_count + ? where phone = ? and DATE(update_time) = CURDATE() ";
                jdbcTemplate.update(updateSql, quantifyCount,quantifyCount, upRankingPhone);
            }
        }catch (Exception e){
            log.error("更新企业产品数据失败: {}", e.getMessage(), e);
        }
    }

    /**
     * 通过 省,市,县、区,镇、街 授权 百分比 更新量化数
     */
    public void updateQuantifyCountByAuthorityPercentage(String phone, BigDecimal value){
        String sql = "SELECT lvel1_proportion,lvel2_proportion,lvel3_proportion,lvel4_proportion,on_off from area_proportion"; // 判断区域授权比例是否开启
        List<Map<String, Object>> areaProportionMap = jdbcTemplate.queryForList(sql);
        if(areaProportionMap != null && areaProportionMap.get(0).get("on_off") != null && areaProportionMap.get(0).get("on_off").toString().equals("0")) {
            sql = "select town_code from mall_b_users where phone = ?";
            String townCode = jdbcTemplate.queryForObject(sql, String.class, phone);
            List<Map<String, Object>> authorizedPhoneFromHightToLow = findAuthorizedPhoneFromHightToLow(townCode);
            for (Map<String, Object> phoneMap : authorizedPhoneFromHightToLow) {
                String proxyPhone = phoneMap.get("phone").toString();
                String level = phoneMap.get("level").toString();
                if(level.equals("1")){
                    BigDecimal lvel1Proportion = new BigDecimal(areaProportionMap.get(0).get("lvel1_proportion").toString());
                    BigDecimal quantifyCount = value.multiply(lvel1Proportion.divide(new BigDecimal(100)));
                    sql = "UPDATE mall_b_users_count SET quantify = quantify + ?,quantify_count = quantify_count + ? WHERE phone = ? and DATE(update_time) = CURDATE()";
                    jdbcTemplate.update(sql, quantifyCount, proxyPhone);
                }else if(level.equals("2")){
                    BigDecimal lvel2Proportion = new BigDecimal(areaProportionMap.get(0).get("lvel2_proportion").toString());
                    BigDecimal quantifyCount = value.multiply(lvel2Proportion.divide(new BigDecimal(100)));
                    sql = "UPDATE mall_b_users_count SET quantify = quantify + ?,quantify_count = quantify_count + ? WHERE phone = ? and DATE(update_time) = CURDATE()";
                    jdbcTemplate.update(sql, quantifyCount, proxyPhone);
                }else if(level.equals("3")){
                    BigDecimal lvel3Proportion = new BigDecimal(areaProportionMap.get(0).get("lvel3_proportion").toString());
                    BigDecimal quantifyCount = value.multiply(lvel3Proportion.divide(new BigDecimal(100)));
                    sql = "UPDATE mall_b_users_count SET quantify = quantify + ?,quantify_count = quantify_count + ? WHERE phone = ? and DATE(update_time) = CURDATE()";
                    jdbcTemplate.update(sql, quantifyCount, proxyPhone);
                }else if(level.equals("4")){
                    BigDecimal lvel4Proportion = new BigDecimal(areaProportionMap.get(0).get("lvel4_proportion").toString());
                    BigDecimal quantifyCount = value.multiply(lvel4Proportion.divide(new BigDecimal(100)));
                    sql = "UPDATE mall_b_users_count SET quantify = quantify + ?,quantify_count = quantify_count + ? WHERE phone = ? and DATE(update_time) = CURDATE()";
                    jdbcTemplate.update(sql, quantifyCount, proxyPhone);
                }
            }
        }
    }

    /**
     * 从省、市、县、乡镇 一级一级往下找代理的手机号
     */
    public List<Map<String, Object>> findAuthorizedPhoneFromHightToLow(String areaId) {
        List<Map<String, Object>> userAreaHierarchy = areaAuthorizeDao.getUserAreaHierarchy(areaId);
        List<Map<String, Object>> phoneList = new ArrayList<>();

        // 从level=1开始按层级查找用户自己所在的省、市、县、乡镇的授权手机号
        for(Map<String, Object> areaInfo : userAreaHierarchy) {
            String currentAreaId = areaInfo.get("id").toString();
            String phone = queryAreaAuthorizePhone(currentAreaId);
            if(isNotEmpty(phone)) {
                Map<String, Object> phoneMap = new HashMap<>();
                phoneMap.put("phone", phone);
                phoneMap.put("level", areaInfo.get("level"));
                phoneList.add(phoneMap);
            }
        }
        return phoneList;
    }

    /**
     * 从area 表的level = 4 往上找代理CB
     */
    public String queryAreaAuthorizePhone(String areaId){
        try {
            String sql = "select phone from area_authorize where type = 'B' and area_id = ? and NOW() >= start_time and NOW() <= end_time";
            return jdbcTemplate.queryForObject(sql, String.class, areaId);
        } catch (Exception e) {
            // 如果没有找到记录，返回null而不是抛出异常
            return null;
        }
    }
}
