package com.mall.project.controller.writeOffData;

import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.writeOffData.WriteOffData;
import com.mall.project.service.writeOffData.WriteOffDataService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 核销数据控制器
 */
@RestController
@Slf4j
@RequestMapping("/api")
public class WriteOffDataController {

    @Autowired
    private WriteOffDataService writeOffDataService;

    /**
     * 查询核销数据,分页显示
     */
    @PostMapping("/queryWriteOffData")
    public CommonResult<CommonPage<Map<String, Object>>> queryWriteOffData(@RequestBody @Valid WriteOffData param) {
        CommonPage<Map<String, Object>> commonPage = writeOffDataService.queryWriteOffDataPages(param.getPhone(), param.getStartDate(), param.getEndDate(), param.getPageNumOrDefault(), param.getPageSizeOrDefault());
        return CommonResult.success(commonPage);
    }

    /**
     * 导出核销数据 Excel
     */
    @PostMapping("/exportWriteOffDataExcel")
    public void exportWriteOffDataExcel(HttpServletResponse response,@RequestBody @Valid WriteOffData param) {
        try {
            // 获取核销数据
            List<Map<String, Object>> dataList = writeOffDataService.exportWriteOffDataExcel(param.getPhone(), param.getStartDate(), param.getEndDate());

            // 获取统计数据
            String todayTotalWriteOffSubsidy = writeOffDataService.todayTotalWriteOffSubsidy(param.getPhone(),param.getStartDate());
            String totalWriteOffSubsidy = writeOffDataService.totalWriteOffSubsidy(param.getPhone(),param.getStartDate());
            String totalUnWriteOffSubsidy = writeOffDataService.totalUnWriteOffSubsidy(param.getPhone(),param.getStartDate());

            // 配置字段映射 (POJO字段名 -> Map键名)
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateDate", "updateDate");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("writeOffSubsidy", "writeOffSubsidy");
            fieldMapping.put("writeOffSubsidyTotal", "writeOffSubsidyTotal");
            fieldMapping.put("unWriteOffSubsidy", "unWriteOffSubsidy");

            // 配置汇总信息（3个统计项在一行显示）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日核销补贴金")
                        .value(todayTotalWriteOffSubsidy)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计核销补贴金")
                        .value(totalWriteOffSubsidy)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计未核销补贴金")
                        .value(totalUnWriteOffSubsidy)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<WriteOffData> config = UniversalExcelExporter.ExportConfig.<WriteOffData>builder()
                    .dataList(dataList)
                    .entityClass(WriteOffData.class)
                    .fileName("核销数据")
                    .sheetName("核销数据")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);
        } catch (Exception e) {
            log.error("导出核销数据异常", e);
        }
    }
}
