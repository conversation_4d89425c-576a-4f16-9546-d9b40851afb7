package com.mall.project.dao.creditEvolve;

import com.mall.project.service.bcSettings.BCSettingsService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 量化值进化数据访问对象
 */
@Repository
public class CreditEvolveDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Autowired
    private BCSettingsService bcSettingsService;

    /**
     * 计算量化值进化
     */
    public void updateCreditEvolve() {
        // 只对用户类型为C的进行量化值进化的计算, 因为B用户量化进化量要从mallB系统读取  CreditEvolveServiceImpl.getCreditEvolveFromMallB()
        String sql = "SELECT v.phone,v.credit_Value FROM quantization_value v,mall_b_users u WHERE v.phone = u.phone and u.user_type = 'C'\n" +
                "AND DATE(v.update_date) = CURDATE() ";
        List<Map<String, Object>> list = jdbcTemplate.queryForList(sql);
        //查询C设置
        Map<String, Object> cSettings = bcSettingsService.getCSettings();
        // 合作企业各IDC设置 的开关
        String isEnabled = cSettings.get("isEnabled").toString();
        // C每ID每日自动信用值进化平台补贴金%
        String creditToCoupon = cSettings.get("creditToCoupon").toString();
        if (isEnabled.equals("0")) {
            for (Map<String, Object> map : list) {
                String phone = (String) map.get("phone");
                //今日信用值
                BigDecimal creditValue = new BigDecimal(map.get("credit_Value").toString());
                //平台补贴金 = 今日信用值 * C每ID每日自动信用值进化平台补贴金%
                BigDecimal platformGold = creditValue.multiply(new BigDecimal(creditToCoupon)).setScale(2, BigDecimal.ROUND_DOWN).divide(new BigDecimal(100));
                //今日量化值进化 = 今日信用值 - 平台补贴金
                BigDecimal creditEvolve = creditValue.subtract(platformGold).setScale(2, BigDecimal.ROUND_DOWN);
                //使用 SELECT EXISTS 判断credit_evolve 表量化值进化量表中是否已经存在 CURDATE() - INTERVAL 1 DAY 的数据 如果存在则更新,不存在则插入
                if (jdbcTemplate.queryForObject("SELECT EXISTS(SELECT 1 FROM credit_evolve WHERE phone = ? AND DATE(update_date) = CURDATE() )", Integer.class, phone) == 0) {
                    String insertSql = "INSERT INTO credit_evolve(phone,credit_evolve,credit_evolve_total,update_date)VALUES(?,?,?,CURDATE() )";
                    jdbcTemplate.update(insertSql, phone, creditEvolve, creditEvolve.add(new BigDecimal(sumCreditEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN));
                }else{
                    String updateSql = "UPDATE credit_evolve SET credit_evolve = ?,credit_evolve_total = ? WHERE phone = ? AND DATE(update_date) = CURDATE() ";
                    jdbcTemplate.update(updateSql, creditEvolve, creditEvolve.add(new BigDecimal(sumCreditEvolve(phone))).setScale(2, BigDecimal.ROUND_DOWN), phone);
                }
            }
        }
    }
    /**
     * 统计 credit_evolve ,phone 的和
     */
    public String sumCreditEvolve(String phone) {
        try{
            String sql = "SELECT SUM(credit_evolve) FROM credit_evolve WHERE phone = ? AND DATE(update_date) < CURDATE() - INTERVAL 1 DAY";
            String sum = jdbcTemplate.queryForObject(sql, String.class, phone);
            return sum == null ? "0" : sum;
        } catch (Exception e) {
            return "0";
        }
    }
    /**
     * 查询量化值进化,分页显示
     */
    public List<Map<String, Object>> queryCreditEvolvePages(String phone, String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT e.update_date,e.phone,u.username,e.credit_evolve,e.credit_evolve_total FROM credit_evolve e LEFT JOIN mall_b_users u ON e.phone = u.phone where 1 = 1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND e.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND e.update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND e.update_date <= CURDATE() ";
        }
        sql += " ORDER BY e.update_date DESC LIMIT " + limit + " OFFSET " + offset;
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 量化值进化, 导出 Excel
     */
    public List<Map<String, Object>> exportCreditEvolveExcel(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT e.update_date,e.phone,u.username,e.credit_evolve,e.credit_evolve_total FROM credit_evolve e LEFT JOIN mall_b_users u ON e.phone = u.phone where 1 = 1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND e.phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND e.update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND e.update_date <= CURDATE() ";
        }
        sql += " ORDER BY e.update_date DESC";
        if(params.isEmpty()){
            return jdbcTemplate.queryForList(sql);
        }else{
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
    /**
     * 量化进化总条数
     */
    public int totalCreditEvolve(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(1) FROM credit_evolve WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND update_date = ?";
            params.add(startDate);
        }else{
            sql += " AND update_date <= CURDATE() ";
        }
        if(params.isEmpty()){
            return jdbcTemplate.queryForObject(sql, Integer.class);
        }else{
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    /**
     * 今日总量化值进化
     */
    public String todayTotalCreditEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        try{
            String sql = "SELECT sum(credit_evolve) as today_total_credit_evolve FROM credit_evolve WHERE 1=1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND update_date = ?";
                params.add(startTime);
            }else{
                sql += " AND update_date = CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        }catch (Exception e){
            return "0";
        }
    }

    /**
     * 累计量化值进化
     */
    public String totalCreditEvolve(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        try{
            String sql = "SELECT sum(credit_evolve_total) as total_credit_evolve FROM credit_evolve WHERE 1=1";
            if (phone != null && !phone.isEmpty()) {
                sql += " AND phone LIKE ?";
                params.add("%" + phone + "%");
            }
            if (startTime != null && !startTime.isEmpty()) {
                sql += " AND update_date <= ?";
                params.add(startTime);
            }else{
                sql += " AND update_date <= CURDATE() - INTERVAL 1 DAY";
            }
            if(params.isEmpty()){
                return jdbcTemplate.queryForObject(sql, String.class);
            }else{
                return jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
        }catch (Exception e) {
            return "0";
        }
    }

    /**
     * 保存从mallB系统获取的量化值进化数据
     */
    public void saveMallBCreditEvolveData(String phone, String creditEvolve, String updateDate) {
        // 检查当天是否已存在该手机号的数据
        String checkSql = "SELECT COUNT(1) FROM credit_evolve WHERE phone = ? AND DATE(update_date) = ?";
        int count = jdbcTemplate.queryForObject(checkSql, Integer.class, phone, updateDate);

        if (count > 0) {
            // 如果存在，则更新
            String updateSql = "UPDATE credit_evolve SET credit_evolve = ?, credit_evolve_total = ? WHERE phone = ? AND DATE(update_date) = ?";
            jdbcTemplate.update(updateSql, creditEvolve, new BigDecimal(creditEvolve).add(new BigDecimal(sumCreditEvolve(phone))), phone, updateDate);
        } else {
            // 如果不存在，则插入
            String insertSql = "INSERT INTO credit_evolve (phone, credit_evolve, credit_evolve_total, update_date) VALUES (?, ?, ?, ?)";
            jdbcTemplate.update(insertSql, phone, creditEvolve, new BigDecimal(creditEvolve).add(new BigDecimal(sumCreditEvolve(phone))), updateDate);
        }
    }
}
