package com.mall.project.controller.quantifyCount;

import com.mall.common.annotation.CurrentUser;
import com.mall.common.api.CommonPage;
import com.mall.common.api.CommonResult;
import com.mall.common.util.UniversalExcelExporter;
import com.mall.project.dto.quantifyCount.QuantifyCount;
import com.mall.project.service.quantifyCount.QuantifyCountService;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api")
@Slf4j
public class QuantifyCountController {

    @Autowired
    private QuantifyCountService quantifyCountService;

    /**
     * 量化数设置查询
     */
    @GetMapping("/getQuantifyCount")
    public CommonResult<Map<String, Object>> getQuantifyCount() {
        Map<String, Object> dataMap = quantifyCountService.getQuantifyCount();
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap);
        }else{
            return CommonResult.failed("未找到C设置信息，请对合作企业各IDC进行设置");
        }
    }
    // 保存或更新量化数设置
    @PutMapping("/saveOrUpdateQuantifyCount")
    public CommonResult<Map<String, Object>> saveOrUpdateQuantifyCount(@RequestBody @Valid QuantifyCount pojo, @CurrentUser Map<String, Object> userInfo) {
        Map<String, Object> dataMap = quantifyCountService.saveOrUpdateQuantifyCount(pojo.getIsEnabled(),pojo.getProportion(),Integer.parseInt(userInfo.get("id").toString()));
        if (dataMap != null && !dataMap.isEmpty()) {
            return CommonResult.success(dataMap,"保存成功");
        }else{
            return CommonResult.failed("保存失败");
        }
    }

    /**
     *  查询量化数设置 分页显示
     */
    @PostMapping("/QueryQuantifyCountPages")
    public CommonResult<CommonPage<Map<String, Object>>> QueryQuantifyCountPages(@RequestBody @Valid QuantifyCount param) {
        CommonPage<Map<String, Object>> commonPage = quantifyCountService.queryQuantifyCountPages(param.getPhone(),  param.getStartTime(), param.getEndTime(), param.getPageNum(), param.getPageSize());
        return CommonResult.success(commonPage);
    }

    /**
     * 导出 量化数 Excel
     */
    @PostMapping("/exportQuantifyCountExcel")
    public void exportQuantifyCountExcel(HttpServletResponse response, @RequestBody @Valid QuantifyCount param) {
        try {
            // 获取累计交易数据
            List<Map<String, Object>> dataList = quantifyCountService.exportQuantifyCountExcel(param.getPhone(),param.getStartTime(), param.getEndTime());
            String todayTotalQuan = quantifyCountService.todayTotalQuan(param.getPhone(),param.getStartTime());
            String weightCountTotal = quantifyCountService.weightCountTotal(param.getPhone(),param.getStartTime());

            // 配置字段映射
            Map<String, String> fieldMapping = new HashMap<>();
            fieldMapping.put("updateTime", "updateTime");
            fieldMapping.put("phone", "phone");
            fieldMapping.put("userName", "username");
            fieldMapping.put("todayQuantifyCount", "quantify");
            fieldMapping.put("todayWeightCount", "Weight");
            fieldMapping.put("totalWeightCount", "weightCount");      // 映射到实体类的 totalWeightCount 字段
            fieldMapping.put("totalQuantifyCount", "quantifyCount");  // 映射到实体类的 totalQuantifyCount 字段

            // 配置汇总信息（一行显示两个统计项）
            List<List<UniversalExcelExporter.SummaryItem>> summaryRows = Arrays.asList(
                Arrays.asList(
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("今日总量化数")
                        .value(todayTotalQuan)
                        .build(),
                    UniversalExcelExporter.SummaryItem.builder()
                        .name("累计量化数")
                        .value(weightCountTotal)
                        .build()
                )
            );

            // 构建导出配置
            UniversalExcelExporter.ExportConfig<QuantifyCount> config = UniversalExcelExporter.ExportConfig.<QuantifyCount>builder()
                    .dataList(dataList)
                    .entityClass(QuantifyCount.class)
                    .fileName("量化数")
                    .sheetName("量化数")
                    .summaryRows(summaryRows)
                    .fieldMapping(fieldMapping)
                    .build();

            // 使用通用导出方法
            UniversalExcelExporter.exportExcel(response, config);

        } catch (Exception e) {
            log.error("导出量化数异常", e);
        }
    }
}
