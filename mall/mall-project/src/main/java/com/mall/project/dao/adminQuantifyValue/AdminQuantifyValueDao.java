package com.mall.project.dao.adminQuantifyValue;

import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static com.mall.common.util.StringUtils.isNotEmpty;

/**
 * Admin 量化值数据访问层
 */
@Repository
@Slf4j
public class AdminQuantifyValueDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    public void saveAdminQuantifyValue(String value){
        // 检查当天是否已存在数据
        String checkSql = "SELECT COUNT(*) FROM admin_quantization_value WHERE phone = 'admin' AND DATE(update_date) = CURDATE() ";
        Integer count = jdbcTemplate.queryForObject(checkSql, Integer.class);
        
        if (count != null && count > 0) {
            // 当天有数据，执行更新
            String updateSql = "UPDATE admin_quantization_value SET value = ? WHERE phone = 'admin' AND DATE(update_date) = CURDATE() ";
            jdbcTemplate.update(updateSql, value);
        } else {
            // 当天无数据，执行插入
            String insertSql = "INSERT INTO admin_quantization_value(phone,value,update_date) VALUES('admin',?,CURDATE() )";
            jdbcTemplate.update(insertSql, value);
        }
    }

    /**
     * 今日Admin量化值
     */
    public String todayAdminAdminQuantifyValue(String phone,String startTime){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(sum(value), '0') as value FROM admin_quantization_value WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startTime != null && !startTime.isEmpty()) {
            sql += " AND DATE(update_date) = ?";
            params.add(startTime);
        }else{
            sql += " AND DATE(update_date) = CURDATE() - INTERVAL 1 DAY";
        }
        try {
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 累计Admin量化值
     */
    public String totalAdminAdminQuantifyValue(String phone,String startDate){
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COALESCE(sum(value), '0') as value FROM admin_quantization_value WHERE 1=1";
        if (phone != null && !phone.isEmpty()) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (startDate != null && !startDate.isEmpty()) {
            sql += " AND DATE(update_date) <= ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_date) <= CURDATE() - INTERVAL 1 DAY";
        }
        try {
            String result;
            if(params.isEmpty()){
                result = jdbcTemplate.queryForObject(sql, String.class);
            }else{
                result = jdbcTemplate.queryForObject(sql, String.class, params.toArray());
            }
            return result != null ? result : "0";
        } catch (Exception e) {
            return "0";
        }
    }

    /**
     * 查询Admin量化值, 分页显示
     */
    public List<Map<String, Object>> queryAdminAdminQuantifyValuePages(String phone, String startDate, String endDate, int limit, int offset) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date, phone, value FROM admin_quantization_value WHERE 1=1";
        
        if (isNotEmpty(phone)) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_date) <= CURDATE() ";
        }
        
        sql += " ORDER BY update_date DESC LIMIT " + limit + " OFFSET " + offset;
        
        if (params.isEmpty()) {
            return jdbcTemplate.queryForList(sql);
        } else {
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }

    /**
     * Admin量化值总条数
     */
    public int totalAdminAdminQuantifyValueCount(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT COUNT(1) FROM admin_quantization_value WHERE 1=1";
        
        if (isNotEmpty(phone)) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_date) <= CURDATE() ";
        }
        
        if (params.isEmpty()) {
            return jdbcTemplate.queryForObject(sql, Integer.class);
        } else {
            return jdbcTemplate.queryForObject(sql, Integer.class, params.toArray());
        }
    }

    /**
     * 导出Admin量化值 Excel
     */
    public List<Map<String, Object>> exportAdminAdminQuantifyValueExcel(String phone, String startDate, String endDate) {
        List<Object> params = new ArrayList<>();
        String sql = "SELECT update_date, phone, value FROM admin_quantization_value WHERE 1=1";
        if (isNotEmpty(phone)) {
            sql += " AND phone LIKE ?";
            params.add("%" + phone + "%");
        }
        if (isNotEmpty(startDate)) {
            sql += " AND DATE(update_date) = ?";
            params.add(startDate);
        }else{
            sql += " AND DATE(update_date) <= CURDATE() ";
        }
        
        sql += " ORDER BY update_date DESC";
        
        if (params.isEmpty()) {
            return jdbcTemplate.queryForList(sql);
        } else {
            return jdbcTemplate.queryForList(sql, params.toArray());
        }
    }
}
