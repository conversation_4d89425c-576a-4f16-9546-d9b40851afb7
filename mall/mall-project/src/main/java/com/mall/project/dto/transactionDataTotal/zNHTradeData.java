package com.mall.project.dto.transactionDataTotal;

import com.alibaba.excel.annotation.ExcelIgnore;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;

/**
 * 描述: 累计交易数据
 */
@Data
public class zNHTradeData {

    @ExcelProperty("时间")
    private String updateTime;         //  交易时间

    @ExcelProperty("手机号")
    private String phone;              //  手机号
    @ExcelProperty("企业名称")
    private String enterpriseName;     //  企业名称
    @ExcelProperty("交易名称")
    private String tradeName;          //  交易名称
    @ExcelProperty("累计交易量")
    private String tradeAmount;        //  累计交易量


    @ExcelIgnore  //Excel导出时忽略此字段
    private String startDate;          //  开始日期

    @ExcelIgnore  //Excel导出时忽略此字段
    private String endDate;            //  结束日期

    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageNum;
    @ExcelIgnore  //Excel导出时忽略此字段
    private Integer pageSize;

    private static final int DEFAULT_PAGE_NUM = 1;
    private static final int DEFAULT_PAGE_SIZE = 10;

    public int getPageNumOrDefault() {
        return (pageNum == null || pageNum < 1) ? DEFAULT_PAGE_NUM : pageNum;
    }
    public int getPageSizeOrDefault() {
        return (pageSize == null || pageSize < 1) ? DEFAULT_PAGE_SIZE : pageSize;
    }
}
